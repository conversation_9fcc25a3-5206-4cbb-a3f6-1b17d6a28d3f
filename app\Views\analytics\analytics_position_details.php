<?php
/**
 * Position-specific Analytics View
 * Shows detailed geographical breakdown for a specific position
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url() ?>">Dashboard</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('analytics/positions') ?>">Analytics</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?= esc($position['designation']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Position Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h3 class="mb-1">
                                <i class="fas fa-briefcase me-2"></i>
                                <?= esc($position['designation']) ?>
                            </h3>
                            <p class="mb-0">
                                <span class="badge bg-light text-dark me-2"><?= esc($position['position_reference']) ?></span>
                                <span class="badge bg-light text-dark me-2"><?= esc($position['classification']) ?></span>
                                <span class="badge bg-light text-dark"><?= esc($position['group_name'] ?? 'No Group') ?></span>
                            </p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <h2 class="mb-0">
                                <i class="fas fa-eye me-2"></i>
                                <?= number_format($position['view_count'] ?? 0) ?>
                            </h2>
                            <p class="mb-0">Total Views</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= count($countryStats) ?></h4>
                            <p class="mb-0">Countries</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-globe fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= count($regionStats) ?></h4>
                            <p class="mb-0">Regions</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-map-marker-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= count($cityStats) ?></h4>
                            <p class="mb-0">Cities</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-city fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= count($coordinates) ?></h4>
                            <p class="mb-0">Unique Locations</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-crosshairs fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Geographical Breakdown -->
    <div class="row mb-4">
        <!-- Country Statistics -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-flag me-2"></i>
                        Views by Country
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($countryStats)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-globe fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No country data available</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Country</th>
                                        <th>Views</th>
                                        <th>Visitors</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($countryStats, 0, 10) as $country): ?>
                                        <tr>
                                            <td>
                                                <i class="fas fa-flag me-1"></i>
                                                <?= esc($country['country'] ?? 'Unknown') ?>
                                                <small class="text-muted">(<?= esc($country['country_code'] ?? 'N/A') ?>)</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary"><?= number_format($country['view_count']) ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?= number_format($country['unique_visitors']) ?></span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Region Statistics -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        Views by Region
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($regionStats)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No region data available</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Region</th>
                                        <th>Country</th>
                                        <th>Views</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($regionStats, 0, 10) as $region): ?>
                                        <tr>
                                            <td>
                                                <i class="fas fa-map-pin me-1"></i>
                                                <?= esc($region['region'] ?? 'Unknown') ?>
                                            </td>
                                            <td>
                                                <small class="text-muted"><?= esc($region['country'] ?? 'Unknown') ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary"><?= number_format($region['view_count']) ?></span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Daily Views Chart -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Daily Views (Last 30 Days)
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="dailyViewsChart" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Coordinates Table -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-crosshairs me-2"></i>
                        Precise Locations
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($coordinates)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-crosshairs fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No coordinate data available</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Location</th>
                                        <th>Coordinates</th>
                                        <th>Views</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($coordinates as $coord): ?>
                                        <tr>
                                            <td>
                                                <i class="fas fa-map-pin me-1"></i>
                                                <?= esc($coord['city'] ?? 'Unknown City') ?>, 
                                                <?= esc($coord['region'] ?? 'Unknown Region') ?>, 
                                                <?= esc($coord['country'] ?? 'Unknown Country') ?>
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    <?= number_format($coord['latitude'], 4) ?>, 
                                                    <?= number_format($coord['longitude'], 4) ?>
                                                </small>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary"><?= number_format($coord['view_count']) ?></span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
$(document).ready(function() {
    // Daily Views Chart
    const dailyData = <?= json_encode($dailyStats) ?>;
    const ctx = document.getElementById('dailyViewsChart').getContext('2d');
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: dailyData.map(item => item.view_date),
            datasets: [{
                label: 'Daily Views',
                data: dailyData.map(item => item.view_count),
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
});
</script>

<?= $this->endSection() ?>

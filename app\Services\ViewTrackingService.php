<?php

namespace App\Services;

use App\Models\PositionViewsModel;
use App\Models\GeoProvincesModel;
use App\Helpers\GeolocationHelper;
use CodeIgniter\HTTP\RequestInterface;

class ViewTrackingService
{
    protected $positionViewsModel;
    protected $geoProvincesModel;
    protected $geolocationHelper;
    protected $request;
    protected $session;

    public function __construct()
    {
        $this->positionViewsModel = new PositionViewsModel();
        $this->geoProvincesModel = new GeoProvincesModel();
        $this->geolocationHelper = new GeolocationHelper();
        $this->request = \Config\Services::request();
        $this->session = \Config\Services::session();
    }

    /**
     * Track a position view
     * 
     * @param int $positionId
     * @param array $options Additional options like force tracking
     * @return bool Success status
     */
    public function trackView($positionId, $options = [])
    {
        try {
            // Get session ID
            $sessionId = $this->getSessionId();
            
            // Check if we should skip duplicate tracking (unless forced)
            if (empty($options['force']) && $this->isDuplicateView($positionId, $sessionId)) {
                return true; // Return true as it's not an error, just skipped
            }

            // Get visitor information
            $viewerIp = $this->getVisitorIp();
            $provinceId = $this->detectProvince($viewerIp, $options);
            $userAgent = $this->getUserAgent();

            // Record the view
            $result = $this->positionViewsModel->recordView(
                $positionId,
                $viewerIp,
                $provinceId,
                $userAgent,
                $sessionId
            );

            return $result !== false;

        } catch (\Exception $e) {
            // Log error but don't break the application
            log_message('error', 'ViewTrackingService::trackView failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get visitor's IP address
     * 
     * @return string|null
     */
    protected function getVisitorIp()
    {
        // Check for various IP headers in order of preference
        $ipHeaders = [
            'HTTP_CF_CONNECTING_IP',     // Cloudflare
            'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
            'HTTP_X_FORWARDED',          // Proxy
            'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
            'HTTP_FORWARDED_FOR',        // Proxy
            'HTTP_FORWARDED',            // Proxy
            'REMOTE_ADDR'                // Standard
        ];

        foreach ($ipHeaders as $header) {
            if (!empty($_SERVER[$header])) {
                $ip = $_SERVER[$header];
                
                // Handle comma-separated IPs (take the first one)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                // Validate IP
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        // Fallback to REMOTE_ADDR even if it's private/reserved
        return $_SERVER['REMOTE_ADDR'] ?? null;
    }

    /**
     * Get user agent string
     * 
     * @return string|null
     */
    protected function getUserAgent()
    {
        return $_SERVER['HTTP_USER_AGENT'] ?? null;
    }

    /**
     * Get or generate session ID
     * 
     * @return string
     */
    protected function getSessionId()
    {
        // Start session if not already started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        return session_id();
    }

    /**
     * Check if this is a duplicate view within the time window
     * 
     * @param int $positionId
     * @param string $sessionId
     * @return bool
     */
    protected function isDuplicateView($positionId, $sessionId)
    {
        return $this->positionViewsModel->hasRecentView($positionId, $sessionId, 60); // 60 minutes window
    }

    /**
     * Detect visitor's province
     * 
     * @param string|null $ip
     * @param array $options
     * @return int|null Province ID
     */
    protected function detectProvince($ip, $options = [])
    {
        // Method 1: Check if province is provided in options
        if (!empty($options['province_id'])) {
            return $options['province_id'];
        }

        // Method 2: Check session for previously detected province
        $sessionProvinceId = $this->session->get('detected_province_id');
        if (!empty($sessionProvinceId)) {
            return $sessionProvinceId;
        }

        // Method 3: Try to detect from IP (basic implementation)
        $provinceId = $this->detectProvinceFromIp($ip);
        
        // Store in session for future use
        if ($provinceId) {
            $this->session->set('detected_province_id', $provinceId);
        }

        return $provinceId;
    }

    /**
     * IP-based province detection using GeolocationHelper
     *
     * @param string|null $ip
     * @return int|null
     */
    protected function detectProvinceFromIp($ip)
    {
        if (empty($ip)) {
            return null;
        }

        return $this->geolocationHelper->detectProvinceFromIp($ip);
    }

    /**
     * Get view count for a position
     * 
     * @param int $positionId
     * @return int
     */
    public function getViewCount($positionId)
    {
        return $this->positionViewsModel->getViewCount($positionId);
    }

    /**
     * Get view counts for multiple positions
     * 
     * @param array $positionIds
     * @return array
     */
    public function getViewCounts($positionIds)
    {
        return $this->positionViewsModel->getViewCounts($positionIds);
    }

    /**
     * Get geographical statistics for a position
     * 
     * @param int $positionId
     * @return array
     */
    public function getGeographicalStats($positionId)
    {
        return $this->positionViewsModel->getGeographicalStats($positionId);
    }

    /**
     * Set province manually (for cases where user selects their location)
     * 
     * @param int $provinceId
     * @return void
     */
    public function setUserProvince($provinceId)
    {
        $this->session->set('user_selected_province_id', $provinceId);
    }

    /**
     * Get user's province (either detected or manually set)
     * 
     * @return int|null
     */
    public function getUserProvince()
    {
        // Prefer manually set province over detected one
        $manualProvinceId = $this->session->get('user_selected_province_id');
        if (!empty($manualProvinceId)) {
            return $manualProvinceId;
        }

        return $this->session->get('detected_province_id');
    }

    /**
     * Clear province detection cache
     * 
     * @return void
     */
    public function clearProvinceCache()
    {
        $this->session->remove('detected_province_id');
        $this->session->remove('user_selected_province_id');
    }
}

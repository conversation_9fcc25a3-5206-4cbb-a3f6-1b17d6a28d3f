-- Migration script to update existing position_views table to support coordinates
-- Run this if you already have the old province-based table structure

-- First, check if the table exists and what columns it has
-- SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'position_views';

-- Add new coordinate and location columns
ALTER TABLE `position_views` 
ADD COLUMN `latitude` decimal(10,8) DEFAULT NULL COMMENT 'Geographical latitude coordinate' AFTER `viewer_ip`,
ADD COLUMN `longitude` decimal(11,8) DEFAULT NULL COMMENT 'Geographical longitude coordinate' AFTER `latitude`,
ADD COLUMN `country` varchar(100) DEFAULT NULL COMMENT 'Country name from geolocation' AFTER `longitude`,
ADD COLUMN `country_code` varchar(2) DEFAULT NULL COMMENT 'ISO country code' AFTER `country`,
ADD COLUMN `region` varchar(100) DEFAULT NULL COMMENT 'Region/state/province name' AFTER `country_code`,
ADD COLUMN `city` varchar(100) DEFAULT NULL COMMENT 'City name' AFTER `region`,
ADD COLUMN `timezone` varchar(50) DEFAULT NULL COMMENT 'Timezone identifier' AFTER `city`;

-- Add indexes for the new columns
ALTER TABLE `position_views` 
ADD KEY `idx_coordinates` (`latitude`, `longitude`),
ADD KEY `idx_country` (`country_code`),
ADD KEY `idx_region` (`region`);

-- Optional: If you want to remove the old province_id column (only if you're sure you don't need it)
-- ALTER TABLE `position_views` DROP FOREIGN KEY `fk_position_views_province_id`;
-- ALTER TABLE `position_views` DROP COLUMN `province_id`;
-- ALTER TABLE `position_views` DROP KEY `idx_province_id`;

-- Sample data migration (if you have existing province data and want to convert it)
-- This is just an example - you would need to customize this based on your actual data
/*
UPDATE position_views pv
JOIN geo_provinces gp ON pv.province_id = gp.id
SET 
    pv.region = gp.name,
    pv.country = 'Papua New Guinea',
    pv.country_code = 'PG',
    -- Add approximate coordinates for major PNG provinces
    pv.latitude = CASE 
        WHEN gp.name = 'National Capital District' THEN -9.4438
        WHEN gp.name = 'Western' THEN -8.0000
        WHEN gp.name = 'Gulf' THEN -7.5000
        WHEN gp.name = 'Central' THEN -9.0000
        WHEN gp.name = 'Milne Bay' THEN -10.5000
        WHEN gp.name = 'Northern' THEN -8.5000
        WHEN gp.name = 'Southern Highlands' THEN -6.0000
        WHEN gp.name = 'Western Highlands' THEN -5.5000
        WHEN gp.name = 'Enga' THEN -5.5000
        WHEN gp.name = 'Chimbu' THEN -6.0000
        WHEN gp.name = 'Eastern Highlands' THEN -6.5000
        WHEN gp.name = 'Morobe' THEN -7.0000
        WHEN gp.name = 'Madang' THEN -5.2000
        WHEN gp.name = 'East Sepik' THEN -4.0000
        WHEN gp.name = 'West Sepik' THEN -4.5000
        WHEN gp.name = 'Manus' THEN -2.0000
        WHEN gp.name = 'New Ireland' THEN -3.0000
        WHEN gp.name = 'East New Britain' THEN -4.5000
        WHEN gp.name = 'West New Britain' THEN -5.5000
        WHEN gp.name = 'Bougainville' THEN -6.0000
        ELSE NULL
    END,
    pv.longitude = CASE 
        WHEN gp.name = 'National Capital District' THEN 147.1803
        WHEN gp.name = 'Western' THEN 142.0000
        WHEN gp.name = 'Gulf' THEN 144.0000
        WHEN gp.name = 'Central' THEN 147.0000
        WHEN gp.name = 'Milne Bay' THEN 150.5000
        WHEN gp.name = 'Northern' THEN 148.0000
        WHEN gp.name = 'Southern Highlands' THEN 143.0000
        WHEN gp.name = 'Western Highlands' THEN 144.0000
        WHEN gp.name = 'Enga' THEN 143.5000
        WHEN gp.name = 'Chimbu' THEN 145.0000
        WHEN gp.name = 'Eastern Highlands' THEN 145.5000
        WHEN gp.name = 'Morobe' THEN 147.0000
        WHEN gp.name = 'Madang' THEN 145.8000
        WHEN gp.name = 'East Sepik' THEN 143.0000
        WHEN gp.name = 'West Sepik' THEN 141.0000
        WHEN gp.name = 'Manus' THEN 147.0000
        WHEN gp.name = 'New Ireland' THEN 152.0000
        WHEN gp.name = 'East New Britain' THEN 152.0000
        WHEN gp.name = 'West New Britain' THEN 150.0000
        WHEN gp.name = 'Bougainville' THEN 155.0000
        ELSE NULL
    END
WHERE pv.province_id IS NOT NULL;
*/

-- Verify the changes
SELECT 
    COUNT(*) as total_records,
    COUNT(latitude) as records_with_coordinates,
    COUNT(country) as records_with_country,
    COUNT(DISTINCT country_code) as unique_countries
FROM position_views;

-- Show sample data
SELECT 
    position_id,
    viewer_ip,
    latitude,
    longitude,
    country,
    country_code,
    region,
    city,
    viewed_at
FROM position_views 
WHERE latitude IS NOT NULL 
LIMIT 10;

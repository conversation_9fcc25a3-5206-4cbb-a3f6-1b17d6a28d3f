<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\PositionViewsModel;
use App\Models\PositionsModel;
use App\Models\ExerciseModel;

/**
 * Position Analytics Controller
 * Handles geographical analytics and position view statistics
 */
class PositionAnalyticsController extends BaseController
{
    protected $positionViewsModel;
    protected $positionsModel;
    protected $exerciseModel;

    public function __construct()
    {
        $this->positionViewsModel = new PositionViewsModel();
        $this->positionsModel = new PositionsModel();
        $this->exerciseModel = new ExerciseModel();
    }

    /**
     * [GET] Main analytics dashboard
     * URI: /analytics/positions
     */
    public function index()
    {
        $orgId = session()->get('org_id');
        if (!$orgId) {
            return redirect()->to('login')->with('error', 'Please login to access analytics');
        }

        // Get exercises for this organization
        $exercises = $this->exerciseModel->where('org_id', $orgId)->findAll();

        // Get overall statistics
        $totalViews = $this->positionViewsModel->countAllResults();
        $countryStats = $this->positionViewsModel->getCountryStats();
        $recentViews = $this->positionViewsModel->where('viewed_at >=', date('Y-m-d H:i:s', strtotime('-7 days')))->countAllResults();

        $data = [
            'title' => 'Position Analytics Dashboard',
            'menu' => 'analytics',
            'exercises' => $exercises,
            'totalViews' => $totalViews,
            'countryStats' => $countryStats,
            'recentViews' => $recentViews
        ];

        return view('analytics/analytics_dashboard', $data);
    }

    /**
     * [GET] Geographical analytics for specific position
     * URI: /analytics/position/{positionId}
     */
    public function positionAnalytics($positionId)
    {
        $orgId = session()->get('org_id');
        if (!$orgId) {
            return redirect()->to('login')->with('error', 'Please login to access analytics');
        }

        // Get position details
        $position = $this->positionsModel->getPositionWithViewCount($positionId);
        if (!$position || $position['org_id'] != $orgId) {
            return redirect()->to('analytics/positions')->with('error', 'Position not found');
        }

        // Get geographical statistics
        $countryStats = $this->positionViewsModel->getCountryStats($positionId);
        $regionStats = $this->positionViewsModel->getRegionStats($positionId);
        $cityStats = $this->positionViewsModel->getCityStats($positionId);
        $coordinates = $this->positionViewsModel->getCoordinatesForMapping($positionId);
        $dailyStats = $this->positionViewsModel->getDailyViewStats($positionId, 30);

        $data = [
            'title' => 'Position Analytics - ' . $position['designation'],
            'menu' => 'analytics',
            'position' => $position,
            'countryStats' => $countryStats,
            'regionStats' => $regionStats,
            'cityStats' => $cityStats,
            'coordinates' => $coordinates,
            'dailyStats' => $dailyStats
        ];

        return view('analytics/analytics_position_details', $data);
    }

    /**
     * [GET] Exercise-wide analytics
     * URI: /analytics/exercise/{exerciseId}
     */
    public function exerciseAnalytics($exerciseId)
    {
        $orgId = session()->get('org_id');
        if (!$orgId) {
            return redirect()->to('login')->with('error', 'Please login to access analytics');
        }

        // Get exercise details
        $exercise = $this->exerciseModel->where('id', $exerciseId)->where('org_id', $orgId)->first();
        if (!$exercise) {
            return redirect()->to('analytics/positions')->with('error', 'Exercise not found');
        }

        // Get positions for this exercise with view counts
        $positions = $this->positionsModel->getPositionsByExerciseIdWithViews($exerciseId);

        // Get position IDs for filtering
        $positionIds = array_column($positions, 'id');

        // Get geographical statistics for all positions in exercise
        $countryStats = [];
        $regionStats = [];
        $totalCoordinates = [];

        if (!empty($positionIds)) {
            // Get aggregated stats for all positions in exercise
            $countryStats = $this->positionViewsModel->select('
                    country,
                    country_code,
                    COUNT(*) as view_count,
                    COUNT(DISTINCT session_id) as unique_visitors
                ')
                ->whereIn('position_id', $positionIds)
                ->where('country IS NOT NULL')
                ->groupBy('country_code')
                ->orderBy('view_count', 'DESC')
                ->findAll();

            $regionStats = $this->positionViewsModel->select('
                    country,
                    country_code,
                    region,
                    COUNT(*) as view_count,
                    COUNT(DISTINCT session_id) as unique_visitors
                ')
                ->whereIn('position_id', $positionIds)
                ->where('region IS NOT NULL')
                ->groupBy('country_code, region')
                ->orderBy('view_count', 'DESC')
                ->findAll();

            $totalCoordinates = $this->positionViewsModel->getCoordinatesForMapping();
            $totalCoordinates = array_filter($totalCoordinates, function($coord) use ($positionIds) {
                return in_array($coord['position_id'] ?? 0, $positionIds);
            });
        }

        $data = [
            'title' => 'Exercise Analytics - ' . $exercise['exercise_name'],
            'menu' => 'analytics',
            'exercise' => $exercise,
            'positions' => $positions,
            'countryStats' => $countryStats,
            'regionStats' => $regionStats,
            'coordinates' => $totalCoordinates
        ];

        return view('analytics/analytics_exercise_details', $data);
    }

    /**
     * [GET] Get coordinates data for mapping (AJAX)
     * URI: /analytics/coordinates/{positionId?}
     */
    public function getCoordinates($positionId = null)
    {
        $coordinates = $this->positionViewsModel->getCoordinatesForMapping($positionId);
        
        return $this->response->setJSON([
            'success' => true,
            'coordinates' => $coordinates
        ]);
    }

    /**
     * [GET] Get country statistics (AJAX)
     * URI: /analytics/countries/{positionId?}
     */
    public function getCountryStats($positionId = null)
    {
        $stats = $this->positionViewsModel->getCountryStats($positionId);
        
        return $this->response->setJSON([
            'success' => true,
            'stats' => $stats
        ]);
    }

    /**
     * [GET] Get region statistics (AJAX)
     * URI: /analytics/regions/{positionId?}
     */
    public function getRegionStats($positionId = null)
    {
        $countryCode = $this->request->getGet('country');
        $stats = $this->positionViewsModel->getRegionStats($positionId, $countryCode);
        
        return $this->response->setJSON([
            'success' => true,
            'stats' => $stats
        ]);
    }
}

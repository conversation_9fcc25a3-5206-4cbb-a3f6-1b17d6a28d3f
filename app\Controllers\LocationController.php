<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Services\ViewTrackingService;
use App\Helpers\GeolocationHelper;

class LocationController extends BaseController
{
    protected $viewTrackingService;
    protected $geolocationHelper;

    public function __construct()
    {
        $this->viewTrackingService = new ViewTrackingService();
        $this->geolocationHelper = new GeolocationHelper();
    }

    /**
     * [GET] Show province selection form
     * URI: /location/select-province
     */
    public function selectProvince()
    {
        $provinces = $this->geolocationHelper->getAllProvincesForSelection();
        $currentProvince = $this->viewTrackingService->getUserProvince();

        $data = [
            'title' => 'Select Your Province',
            'provinces' => $provinces,
            'currentProvince' => $currentProvince
        ];

        return view('location/location_select_province', $data);
    }

    /**
     * [POST] Set user's province
     * URI: /location/set-province
     */
    public function setProvince()
    {
        $provinceId = $this->request->getPost('province_id');
        $redirectUrl = $this->request->getPost('redirect_url') ?? base_url();

        // Validate province ID
        if (empty($provinceId) || !is_numeric($provinceId)) {
            return redirect()->back()->with('error', 'Please select a valid province.');
        }

        // Verify province exists
        $province = $this->geolocationHelper->getProvinceById($provinceId);
        if (!$province) {
            return redirect()->back()->with('error', 'Selected province not found.');
        }

        // Set the province
        $this->viewTrackingService->setUserProvince($provinceId);

        // Redirect back with success message
        return redirect()->to($redirectUrl)->with('success', 'Your location has been set to ' . $province['name']);
    }

    /**
     * [GET] Get current user province (AJAX endpoint)
     * URI: /location/current-province
     */
    public function getCurrentProvince()
    {
        $provinceId = $this->viewTrackingService->getUserProvince();
        $province = null;

        if ($provinceId) {
            $province = $this->geolocationHelper->getProvinceById($provinceId);
        }

        return $this->response->setJSON([
            'success' => true,
            'province' => $province
        ]);
    }

    /**
     * [POST] Clear province cache
     * URI: /location/clear-province
     */
    public function clearProvince()
    {
        $this->viewTrackingService->clearProvinceCache();
        
        return redirect()->back()->with('success', 'Location cache cleared.');
    }
}

<?php

namespace App\Models;

use CodeIgniter\Model;

class PositionViewsModel extends Model
{
    protected $table         = 'position_views';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $returnType    = 'array';
    protected $useSoftDeletes = true;
    protected $deletedField = 'deleted_at';

    // Fields that are allowed to be set during insert/update operations
    protected $allowedFields = [
        'position_id',
        'viewer_ip',
        'province_id',
        'user_agent',
        'session_id',
        'viewed_at',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation rules
    protected $validationRules = [
        'position_id' => 'required|integer|is_not_unique[positions.id]',
        'viewer_ip'   => 'permit_empty|valid_ip',
        'province_id' => 'permit_empty|integer|is_not_unique[geo_provinces.id]',
        'session_id'  => 'permit_empty|max_length[128]',
        'viewed_at'   => 'required|valid_date'
    ];

    protected $validationMessages = [
        'position_id' => [
            'required' => 'Position ID is required',
            'integer'  => 'Position ID must be an integer',
            'is_not_unique' => 'Position does not exist'
        ],
        'viewer_ip' => [
            'valid_ip' => 'Invalid IP address format'
        ],
        'province_id' => [
            'integer' => 'Province ID must be an integer',
            'is_not_unique' => 'Province does not exist'
        ]
    ];

    /**
     * Record a new position view
     * 
     * @param int $positionId
     * @param string|null $viewerIp
     * @param int|null $provinceId
     * @param string|null $userAgent
     * @param string|null $sessionId
     * @return bool|int Returns insert ID on success, false on failure
     */
    public function recordView($positionId, $viewerIp = null, $provinceId = null, $userAgent = null, $sessionId = null)
    {
        $data = [
            'position_id' => $positionId,
            'viewer_ip'   => $viewerIp,
            'province_id' => $provinceId,
            'user_agent'  => $userAgent,
            'session_id'  => $sessionId,
            'viewed_at'   => date('Y-m-d H:i:s')
        ];

        return $this->insert($data);
    }

    /**
     * Check if a position has been viewed by this session recently (within last hour)
     * 
     * @param int $positionId
     * @param string $sessionId
     * @param int $withinMinutes Default 60 minutes
     * @return bool
     */
    public function hasRecentView($positionId, $sessionId, $withinMinutes = 60)
    {
        if (empty($sessionId)) {
            return false;
        }

        $cutoffTime = date('Y-m-d H:i:s', strtotime("-{$withinMinutes} minutes"));
        
        $view = $this->where('position_id', $positionId)
                     ->where('session_id', $sessionId)
                     ->where('viewed_at >=', $cutoffTime)
                     ->first();

        return !empty($view);
    }

    /**
     * Get total view count for a specific position
     * 
     * @param int $positionId
     * @return int
     */
    public function getViewCount($positionId)
    {
        return $this->where('position_id', $positionId)->countAllResults();
    }

    /**
     * Get view counts for multiple positions
     * 
     * @param array $positionIds
     * @return array Array with position_id as key and view_count as value
     */
    public function getViewCounts($positionIds)
    {
        if (empty($positionIds)) {
            return [];
        }

        $results = $this->select('position_id, COUNT(*) as view_count')
                        ->whereIn('position_id', $positionIds)
                        ->groupBy('position_id')
                        ->findAll();

        $viewCounts = [];
        foreach ($results as $result) {
            $viewCounts[$result['position_id']] = (int)$result['view_count'];
        }

        // Ensure all requested positions have a count (even if 0)
        foreach ($positionIds as $positionId) {
            if (!isset($viewCounts[$positionId])) {
                $viewCounts[$positionId] = 0;
            }
        }

        return $viewCounts;
    }

    /**
     * Get geographical statistics for a position
     * 
     * @param int $positionId
     * @return array
     */
    public function getGeographicalStats($positionId)
    {
        return $this->select('
                geo_provinces.name as province_name,
                geo_provinces.province_code,
                COUNT(position_views.id) as view_count
            ')
            ->join('geo_provinces', 'position_views.province_id = geo_provinces.id', 'left')
            ->where('position_views.position_id', $positionId)
            ->groupBy('position_views.province_id')
            ->orderBy('view_count', 'DESC')
            ->findAll();
    }

    /**
     * Get recent views for a position (last 30 days by default)
     * 
     * @param int $positionId
     * @param int $days
     * @return array
     */
    public function getRecentViews($positionId, $days = 30)
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        return $this->select('
                position_views.*,
                geo_provinces.name as province_name,
                geo_provinces.province_code
            ')
            ->join('geo_provinces', 'position_views.province_id = geo_provinces.id', 'left')
            ->where('position_views.position_id', $positionId)
            ->where('position_views.viewed_at >=', $cutoffDate)
            ->orderBy('position_views.viewed_at', 'DESC')
            ->findAll();
    }

    /**
     * Get daily view statistics for a position
     * 
     * @param int $positionId
     * @param int $days Number of days to look back
     * @return array
     */
    public function getDailyViewStats($positionId, $days = 30)
    {
        $cutoffDate = date('Y-m-d', strtotime("-{$days} days"));
        
        return $this->select('
                DATE(viewed_at) as view_date,
                COUNT(*) as view_count
            ')
            ->where('position_id', $positionId)
            ->where('DATE(viewed_at) >=', $cutoffDate)
            ->groupBy('DATE(viewed_at)')
            ->orderBy('view_date', 'ASC')
            ->findAll();
    }
}

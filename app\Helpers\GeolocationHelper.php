<?php

namespace App\Helpers;

use App\Models\GeoProvincesModel;

class GeolocationHelper
{
    protected $geoProvincesModel;

    public function __construct()
    {
        $this->geoProvincesModel = new GeoProvincesModel();
    }

    /**
     * Get geographical coordinates and location data from IP address
     *
     * @param string $ip
     * @return array|null Location data with coordinates
     */
    public function getLocationFromIp($ip)
    {
        if (empty($ip) || !filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
            return null;
        }

        try {
            // Method 1: Try ipapi.co (free tier: 1000 requests/day)
            $locationData = $this->tryIpApiCo($ip);
            if ($locationData) {
                return $locationData;
            }

            // Method 2: Try ip-api.com (free tier: 1000 requests/hour)
            $locationData = $this->tryIpApiCom($ip);
            if ($locationData) {
                return $locationData;
            }

            // Method 3: Try ipinfo.io (free tier: 50,000 requests/month)
            $locationData = $this->tryIpInfoIo($ip);
            if ($locationData) {
                return $locationData;
            }

        } catch (\Exception $e) {
            log_message('error', 'GeolocationHelper::getLocationFromIp failed: ' . $e->getMessage());
        }

        return null;
    }

    /**
     * Detect province from IP address (backward compatibility)
     *
     * @param string $ip
     * @return int|null Province ID
     */
    public function detectProvinceFromIp($ip)
    {
        $locationData = $this->getLocationFromIp($ip);
        if ($locationData && isset($locationData['region'])) {
            return $this->mapRegionToProvince($locationData['region'], $locationData['country_code'] ?? null);
        }
        return null;
    }

    /**
     * Try ipapi.co service
     *
     * @param string $ip
     * @return array|null
     */
    protected function tryIpApiCo($ip)
    {
        $url = "https://ipapi.co/{$ip}/json/";
        $response = $this->makeHttpRequest($url);

        if ($response && isset($response['latitude'], $response['longitude'])) {
            return [
                'ip' => $ip,
                'latitude' => (float)$response['latitude'],
                'longitude' => (float)$response['longitude'],
                'country' => $response['country_name'] ?? null,
                'country_code' => $response['country_code'] ?? null,
                'region' => $response['region'] ?? null,
                'city' => $response['city'] ?? null,
                'timezone' => $response['timezone'] ?? null
            ];
        }

        return null;
    }

    /**
     * Try ip-api.com service
     *
     * @param string $ip
     * @return array|null
     */
    protected function tryIpApiCom($ip)
    {
        $url = "http://ip-api.com/json/{$ip}?fields=status,country,countryCode,region,regionName,city,lat,lon,timezone";
        $response = $this->makeHttpRequest($url);

        if ($response && $response['status'] === 'success' && isset($response['lat'], $response['lon'])) {
            return [
                'ip' => $ip,
                'latitude' => (float)$response['lat'],
                'longitude' => (float)$response['lon'],
                'country' => $response['country'] ?? null,
                'country_code' => $response['countryCode'] ?? null,
                'region' => $response['regionName'] ?? $response['region'] ?? null,
                'city' => $response['city'] ?? null,
                'timezone' => $response['timezone'] ?? null
            ];
        }

        return null;
    }

    /**
     * Try ipinfo.io service
     *
     * @param string $ip
     * @return array|null
     */
    protected function tryIpInfoIo($ip)
    {
        $url = "https://ipinfo.io/{$ip}/json";
        $response = $this->makeHttpRequest($url);

        if ($response && isset($response['loc'])) {
            $coordinates = explode(',', $response['loc']);
            if (count($coordinates) === 2) {
                return [
                    'ip' => $ip,
                    'latitude' => (float)$coordinates[0],
                    'longitude' => (float)$coordinates[1],
                    'country' => $this->getCountryName($response['country'] ?? null),
                    'country_code' => $response['country'] ?? null,
                    'region' => $response['region'] ?? null,
                    'city' => $response['city'] ?? null,
                    'timezone' => $response['timezone'] ?? null
                ];
            }
        }

        return null;
    }

    /**
     * Get full country name from country code
     *
     * @param string|null $countryCode
     * @return string|null
     */
    protected function getCountryName($countryCode)
    {
        if (!$countryCode) return null;

        $countries = [
            'PG' => 'Papua New Guinea',
            'AU' => 'Australia',
            'US' => 'United States',
            'ID' => 'Indonesia',
            'PH' => 'Philippines',
            'MY' => 'Malaysia',
            'SG' => 'Singapore',
            'NZ' => 'New Zealand',
            'FJ' => 'Fiji',
            'SB' => 'Solomon Islands',
            'VU' => 'Vanuatu',
            'TO' => 'Tonga',
            'WS' => 'Samoa',
            'PW' => 'Palau',
            'FM' => 'Micronesia',
            'MH' => 'Marshall Islands',
            'KI' => 'Kiribati',
            'NR' => 'Nauru',
            'TV' => 'Tuvalu'
        ];

        return $countries[$countryCode] ?? $countryCode;
    }

    /**
     * Make HTTP request with timeout and error handling
     * 
     * @param string $url
     * @return array|null
     */
    protected function makeHttpRequest($url)
    {
        $context = stream_context_create([
            'http' => [
                'timeout' => 5, // 5 second timeout
                'user_agent' => 'DERS Position Tracking System'
            ]
        ]);

        $response = @file_get_contents($url, false, $context);
        
        if ($response === false) {
            return null;
        }

        $data = json_decode($response, true);
        return is_array($data) ? $data : null;
    }

    /**
     * Map region name to province ID in our database
     * 
     * @param string $regionName
     * @param string|null $countryCode
     * @return int|null
     */
    protected function mapRegionToProvince($regionName, $countryCode = null)
    {
        if (empty($regionName)) {
            return null;
        }

        // First try exact match
        $province = $this->geoProvincesModel->where('name', $regionName)->first();
        if ($province) {
            return $province['id'];
        }

        // Try case-insensitive match
        $province = $this->geoProvincesModel->like('name', $regionName)->first();
        if ($province) {
            return $province['id'];
        }

        // For Papua New Guinea, try mapping common region names
        if ($countryCode === 'PG' || $countryCode === 'PNG') {
            $pngMapping = [
                'National Capital District' => 'National Capital District',
                'NCD' => 'National Capital District',
                'Port Moresby' => 'National Capital District',
                'Western Province' => 'Western',
                'Gulf Province' => 'Gulf',
                'Central Province' => 'Central',
                'Milne Bay Province' => 'Milne Bay',
                'Oro Province' => 'Northern',
                'Northern Province' => 'Northern',
                'Southern Highlands Province' => 'Southern Highlands',
                'Western Highlands Province' => 'Western Highlands',
                'Enga Province' => 'Enga',
                'Chimbu Province' => 'Chimbu',
                'Eastern Highlands Province' => 'Eastern Highlands',
                'Morobe Province' => 'Morobe',
                'Madang Province' => 'Madang',
                'East Sepik Province' => 'East Sepik',
                'Sandaun Province' => 'West Sepik',
                'West Sepik Province' => 'West Sepik',
                'Manus Province' => 'Manus',
                'New Ireland Province' => 'New Ireland',
                'East New Britain Province' => 'East New Britain',
                'West New Britain Province' => 'West New Britain',
                'Bougainville Province' => 'Bougainville'
            ];

            if (isset($pngMapping[$regionName])) {
                $mappedName = $pngMapping[$regionName];
                $province = $this->geoProvincesModel->where('name', $mappedName)->first();
                if ($province) {
                    return $province['id'];
                }
            }
        }

        return null;
    }

    /**
     * Get all provinces for dropdown/selection
     * 
     * @return array
     */
    public function getAllProvincesForSelection()
    {
        return $this->geoProvincesModel->getAllProvinces();
    }

    /**
     * Get province by ID
     * 
     * @param int $provinceId
     * @return array|null
     */
    public function getProvinceById($provinceId)
    {
        return $this->geoProvincesModel->find($provinceId);
    }

    /**
     * Check if IP is from Papua New Guinea (basic check)
     * 
     * @param string $ip
     * @return bool
     */
    public function isPapuaNewGuineaIp($ip)
    {
        // This is a basic implementation
        // In production, you'd use a proper IP geolocation database
        
        // PNG IP ranges (simplified - this is not comprehensive)
        $pngRanges = [
            '***********/19',    // DataCo
            '***********/16',    // Various PNG ISPs
            '*********/16',      // Telikom PNG
        ];

        foreach ($pngRanges as $range) {
            if ($this->ipInRange($ip, $range)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if IP is in CIDR range
     * 
     * @param string $ip
     * @param string $range
     * @return bool
     */
    protected function ipInRange($ip, $range)
    {
        list($subnet, $bits) = explode('/', $range);
        $ip = ip2long($ip);
        $subnet = ip2long($subnet);
        $mask = -1 << (32 - $bits);
        $subnet &= $mask;
        return ($ip & $mask) == $subnet;
    }
}

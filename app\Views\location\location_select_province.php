<?php
/**
 * Province Selection View
 * Allows users to manually select their province for better tracking
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url() ?>">Home</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Select Province
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                        Select Your Province
                    </h2>
                    <p class="text-muted mb-0">
                        Help us provide better location-based services by selecting your province.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Province Selection Form -->
    <div class="row justify-content-center">
        <div class="col-lg-6 col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-location-dot me-2"></i>
                        Choose Your Province
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <?= session()->getFlashdata('error') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <?= session()->getFlashdata('success') ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($currentProvince): ?>
                        <div class="alert alert-info" role="alert">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Current Location:</strong> 
                            <?php 
                            $current = null;
                            foreach ($provinces as $province) {
                                if ($province['id'] == $currentProvince) {
                                    $current = $province;
                                    break;
                                }
                            }
                            echo $current ? esc($current['name']) : 'Unknown';
                            ?>
                        </div>
                    <?php endif; ?>

                    <form action="<?= base_url('location/set-province') ?>" method="post">
                        <?= csrf_field() ?>
                        
                        <input type="hidden" name="redirect_url" value="<?= esc($this->request->getGet('redirect') ?? previous_url()) ?>">

                        <div class="mb-3">
                            <label for="province_id" class="form-label">
                                <i class="fas fa-map me-1"></i>
                                Select Province <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="province_id" name="province_id" required>
                                <option value="">-- Choose your province --</option>
                                <?php foreach ($provinces as $province): ?>
                                    <option value="<?= $province['id'] ?>" 
                                            <?= ($currentProvince == $province['id']) ? 'selected' : '' ?>>
                                        <?= esc($province['name']) ?>
                                        <?php if (!empty($province['province_code'])): ?>
                                            (<?= esc($province['province_code']) ?>)
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">
                                <i class="fas fa-lightbulb me-1"></i>
                                This helps us provide location-relevant job opportunities and statistics.
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="<?= esc($this->request->getGet('redirect') ?? previous_url()) ?>" 
                               class="btn btn-secondary me-md-2">
                                <i class="fas fa-times me-1"></i>
                                Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i>
                                Save Location
                            </button>
                        </div>
                    </form>

                    <?php if ($currentProvince): ?>
                        <hr class="my-4">
                        <div class="text-center">
                            <form action="<?= base_url('location/clear-province') ?>" method="post" class="d-inline">
                                <?= csrf_field() ?>
                                <button type="submit" class="btn btn-outline-warning btn-sm" 
                                        onclick="return confirm('Are you sure you want to clear your location data?')">
                                    <i class="fas fa-trash me-1"></i>
                                    Clear Location Data
                                </button>
                            </form>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Information Section -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-info">
                <div class="card-body">
                    <h6 class="card-title text-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Why do we need your location?
                    </h6>
                    <ul class="mb-0">
                        <li>To provide location-relevant job opportunities</li>
                        <li>To generate statistics about job interest by region</li>
                        <li>To help employers understand their reach</li>
                        <li>Your privacy is important - we only store province-level data</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Auto-submit form when province is selected (optional enhancement)
    $('#province_id').on('change', function() {
        if ($(this).val()) {
            // Optional: Show confirmation before auto-submit
            // $(this).closest('form').submit();
        }
    });
});
</script>

<?= $this->endSection() ?>

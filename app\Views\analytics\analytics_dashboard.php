<?php
/**
 * Position Analytics Dashboard
 * Shows geographical breakdown of position views with interactive maps and charts
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url() ?>">Dashboard</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Position Analytics
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-chart-line me-2 text-primary"></i>
                        Position Analytics Dashboard
                    </h2>
                    <p class="text-muted mb-0">
                        Geographical insights and visitor statistics for your job positions
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= number_format($totalViews) ?></h4>
                            <p class="mb-0">Total Views</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-eye fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= number_format($recentViews) ?></h4>
                            <p class="mb-0">Views (Last 7 Days)</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-week fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= count($countryStats) ?></h4>
                            <p class="mb-0">Countries Reached</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-globe fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?= count($exercises) ?></h4>
                            <p class="mb-0">Active Exercises</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-briefcase fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Country Statistics -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-flag me-2"></i>
                        Views by Country
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($countryStats)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-globe fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Geographical Data Available</h5>
                            <p class="text-muted">Position views will appear here once visitors access your positions.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Country</th>
                                        <th>Code</th>
                                        <th>Total Views</th>
                                        <th>Unique Visitors</th>
                                        <th>Percentage</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $totalCountryViews = array_sum(array_column($countryStats, 'view_count'));
                                    foreach ($countryStats as $country): 
                                        $percentage = $totalCountryViews > 0 ? ($country['view_count'] / $totalCountryViews) * 100 : 0;
                                    ?>
                                        <tr>
                                            <td>
                                                <i class="fas fa-flag me-2"></i>
                                                <?= esc($country['country'] ?? 'Unknown') ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary"><?= esc($country['country_code'] ?? 'N/A') ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-primary"><?= number_format($country['view_count']) ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-info"><?= number_format($country['unique_visitors']) ?></span>
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar" role="progressbar" 
                                                         style="width: <?= $percentage ?>%" 
                                                         aria-valuenow="<?= $percentage ?>" 
                                                         aria-valuemin="0" aria-valuemax="100">
                                                        <?= number_format($percentage, 1) ?>%
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Exercises List -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        Exercise Analytics
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($exercises)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-briefcase fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Exercises Found</h5>
                            <p class="text-muted">Create exercises to see their analytics here.</p>
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach ($exercises as $exercise): ?>
                                <div class="col-lg-4 col-md-6 mb-3">
                                    <div class="card border-left-primary">
                                        <div class="card-body">
                                            <h6 class="card-title text-primary">
                                                <?= esc($exercise['exercise_name']) ?>
                                            </h6>
                                            <p class="card-text text-muted small">
                                                <strong>Advertisement:</strong> <?= esc($exercise['advertisement_no']) ?><br>
                                                <strong>Status:</strong> 
                                                <span class="badge bg-<?= $exercise['status'] === 'published' ? 'success' : 'secondary' ?>">
                                                    <?= ucfirst($exercise['status']) ?>
                                                </span>
                                            </p>
                                            <div class="d-grid">
                                                <a href="<?= base_url('analytics/exercise/' . $exercise['id']) ?>" 
                                                   class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-chart-bar me-1"></i>
                                                    View Analytics
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js for future chart implementations -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
$(document).ready(function() {
    // Initialize any charts or interactive elements here
    console.log('Analytics dashboard loaded');
});
</script>

<?= $this->endSection() ?>

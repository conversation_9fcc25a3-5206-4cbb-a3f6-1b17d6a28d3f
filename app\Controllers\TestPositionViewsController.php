<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Services\ViewTrackingService;
use App\Models\PositionViewsModel;
use App\Models\PositionsModel;

/**
 * Test controller for position views functionality
 * This controller is for testing purposes only
 */
class TestPositionViewsController extends BaseController
{
    protected $viewTrackingService;
    protected $positionViewsModel;
    protected $positionsModel;

    public function __construct()
    {
        $this->viewTrackingService = new ViewTrackingService();
        $this->positionViewsModel = new PositionViewsModel();
        $this->positionsModel = new PositionsModel();
    }

    /**
     * [GET] Test position views functionality
     * URI: /test/position-views
     */
    public function index()
    {
        $results = [];

        try {
            // Test 1: Record a view
            $positionId = 1; // Use a position ID that exists in your database
            $trackResult = $this->viewTrackingService->trackView($positionId);
            $results['track_view'] = [
                'success' => $trackResult,
                'message' => $trackResult ? 'View tracked successfully' : 'Failed to track view'
            ];

            // Test 2: Get view count
            $viewCount = $this->viewTrackingService->getViewCount($positionId);
            $results['view_count'] = [
                'position_id' => $positionId,
                'count' => $viewCount
            ];

            // Test 3: Test duplicate view prevention
            $duplicateResult = $this->viewTrackingService->trackView($positionId);
            $results['duplicate_prevention'] = [
                'success' => $duplicateResult,
                'message' => $duplicateResult ? 'Duplicate view tracked (should be prevented)' : 'Duplicate view prevented (correct behavior)'
            ];

            // Test 4: Get positions with view counts
            $positionsWithViews = $this->positionsModel->getPositionsWithViewStats([$positionId]);
            $results['positions_with_views'] = $positionsWithViews;

            // Test 5: Test geographical stats
            $geoStats = $this->viewTrackingService->getGeographicalStats($positionId);
            $results['geographical_stats'] = $geoStats;

        } catch (\Exception $e) {
            $results['error'] = [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ];
        }

        return $this->response->setJSON([
            'success' => true,
            'results' => $results,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * [GET] Test database connection and table existence
     * URI: /test/position-views/database
     */
    public function testDatabase()
    {
        $results = [];

        try {
            // Test 1: Check if position_views table exists
            $db = \Config\Database::connect();
            $tableExists = $db->tableExists('position_views');
            $results['table_exists'] = $tableExists;

            if ($tableExists) {
                // Test 2: Check table structure
                $fields = $db->getFieldData('position_views');
                $results['table_structure'] = $fields;

                // Test 3: Count existing records
                $recordCount = $this->positionViewsModel->countAllResults();
                $results['record_count'] = $recordCount;

                // Test 4: Test basic insert
                $testData = [
                    'position_id' => 1,
                    'viewer_ip' => '127.0.0.1',
                    'latitude' => -9.4438,
                    'longitude' => 147.1803,
                    'country' => 'Papua New Guinea',
                    'country_code' => 'PG',
                    'region' => 'National Capital District',
                    'city' => 'Port Moresby',
                    'session_id' => 'test_session_' . time(),
                    'viewed_at' => date('Y-m-d H:i:s')
                ];
                
                $insertResult = $this->positionViewsModel->insert($testData);
                $results['test_insert'] = [
                    'success' => $insertResult !== false,
                    'insert_id' => $insertResult
                ];
            }

        } catch (\Exception $e) {
            $results['error'] = [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ];
        }

        return $this->response->setJSON([
            'success' => true,
            'results' => $results,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * [GET] Test view tracking with different scenarios
     * URI: /test/position-views/scenarios
     */
    public function testScenarios()
    {
        $results = [];

        try {
            $positionId = 1;

            // Scenario 1: Normal view tracking
            $result1 = $this->viewTrackingService->trackView($positionId);
            $results['scenario_1_normal'] = [
                'success' => $result1,
                'description' => 'Normal view tracking'
            ];

            // Scenario 2: Force tracking (bypass duplicate prevention)
            $result2 = $this->viewTrackingService->trackView($positionId, ['force' => true]);
            $results['scenario_2_forced'] = [
                'success' => $result2,
                'description' => 'Forced view tracking (bypass duplicate prevention)'
            ];

            // Scenario 3: View tracking with coordinates
            $result3 = $this->viewTrackingService->trackView($positionId, [
                'latitude' => -6.2088,
                'longitude' => 106.8456,
                'country' => 'Indonesia',
                'country_code' => 'ID',
                'region' => 'Jakarta',
                'city' => 'Jakarta',
                'force' => true
            ]);
            $results['scenario_3_with_coordinates'] = [
                'success' => $result3,
                'description' => 'View tracking with coordinates specified'
            ];

            // Scenario 4: Invalid position ID
            $result4 = $this->viewTrackingService->trackView(99999);
            $results['scenario_4_invalid_position'] = [
                'success' => $result4,
                'description' => 'View tracking with invalid position ID'
            ];

            // Get final view count
            $finalCount = $this->viewTrackingService->getViewCount($positionId);
            $results['final_view_count'] = $finalCount;

        } catch (\Exception $e) {
            $results['error'] = [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ];
        }

        return $this->response->setJSON([
            'success' => true,
            'results' => $results,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * [GET] Clear test data
     * URI: /test/position-views/clear
     */
    public function clearTestData()
    {
        try {
            // Delete test records (those with test session IDs)
            $db = \Config\Database::connect();
            $builder = $db->table('position_views');
            $deletedCount = $builder->like('session_id', 'test_session_')->delete();

            return $this->response->setJSON([
                'success' => true,
                'message' => "Cleared {$deletedCount} test records",
                'timestamp' => date('Y-m-d H:i:s')
            ]);

        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'error' => $e->getMessage(),
                'timestamp' => date('Y-m-d H:i:s')
            ]);
        }
    }
}
